import type { ISchoolStatistics, ITeacherRanking } from '@/types/statistics';
import { Card } from 'antd';
import React from 'react';
import SchoolOverview from './components/SchoolOverview';
import TeacherRanking from './components/TeacherRanking';

/**
 * 统计分析页面演示组件
 * 用于展示各个组件的功能和样式
 */
const StatisticsDemo: React.FC = () => {
  // 模拟学校统计数据
  const mockSchoolData: ISchoolStatistics = {
    sso_school_code: 'school_001',
    sso_school_name: '示例小学',
    month: '2024-01',
    total_responses: 150,
    completed_responses: 135,
    completion_rate: 90.0,
    school_average_score: 88.5,
    teacher_average_score: 87.2,
    total_teachers_evaluated: 25,
  };

  // 模拟教师排名数据
  const mockTeacherRanking: ITeacherRanking[] = [
    {
      sso_teacher_id: 'teacher_001',
      sso_teacher_name: '张老师',
      sso_teacher_subject: '语文',
      sso_teacher_department: '语文组',
      average_score: 92.5,
      evaluation_count: 28,
      recommendation_rate: 95.2,
      rank: 1,
    },
    {
      sso_teacher_id: 'teacher_002',
      sso_teacher_name: '李老师',
      sso_teacher_subject: '数学',
      sso_teacher_department: '数学组',
      average_score: 91.8,
      evaluation_count: 25,
      recommendation_rate: 94.1,
      rank: 2,
    },
    {
      sso_teacher_id: 'teacher_003',
      sso_teacher_name: '王老师',
      sso_teacher_subject: '英语',
      sso_teacher_department: '英语组',
      average_score: 90.2,
      evaluation_count: 22,
      recommendation_rate: 92.8,
      rank: 3,
    },
    {
      sso_teacher_id: 'teacher_004',
      sso_teacher_name: '赵老师',
      sso_teacher_subject: '科学',
      sso_teacher_department: '科学组',
      average_score: 89.5,
      evaluation_count: 20,
      recommendation_rate: 91.5,
      rank: 4,
    },
    {
      sso_teacher_id: 'teacher_005',
      sso_teacher_name: '陈老师',
      sso_teacher_subject: '体育',
      sso_teacher_department: '体育组',
      average_score: 88.9,
      evaluation_count: 18,
      recommendation_rate: 90.2,
      rank: 5,
    },
  ];

  const handleTeacherClick = (teacherId: string) => {
    console.log('点击教师:', teacherId);
  };

  return (
    <div style={{ padding: '24px', background: '#f5f5f5' }}>
      <Card title="统计分析页面演示" style={{ marginBottom: 16 }}>
        <p>这是统计分析页面的演示版本，展示了各个组件的功能和样式。</p>
      </Card>

      {/* 学校整体统计 */}
      <SchoolOverview data={mockSchoolData} loading={false} />

      {/* 教师排行榜 */}
      <TeacherRanking
        data={mockTeacherRanking}
        total={mockTeacherRanking.length}
        loading={false}
        onTeacherClick={handleTeacherClick}
      />
    </div>
  );
};

export default StatisticsDemo;
