import type {
  IKeywordData,
  IScoreDistribution,
  ITeacherStatistics,
} from '@/types/statistics';
import { Pie, WordCloud } from '@ant-design/charts';
import {
  HeartOutlined,
  MessageOutlined,
  TrophyOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  Card,
  Col,
  Empty,
  Modal,
  Row,
  Space,
  Spin,
  Statistic,
  Tag,
} from 'antd';
import React from 'react';

interface TeacherDetailModalProps {
  visible: boolean;
  loading?: boolean;
  teacherDetail: ITeacherStatistics | null;
  scoreDistribution: IScoreDistribution[];
  keywordData: IKeywordData[];
  onClose: () => void;
}

/**
 * 教师详情模态框组件
 */
const TeacherDetailModal: React.FC<TeacherDetailModalProps> = ({
  visible,
  loading = false,
  teacherDetail,
  scoreDistribution,
  keywordData,
  onClose,
}) => {
  // 评分分布饼图配置
  const pieConfig = {
    data: scoreDistribution.map((item) => ({
      type: item.score_range,
      value: item.percentage,
      count: item.count,
    })),
    angleField: 'value',
    colorField: 'type',
    radius: 0.8,
    label: {
      type: 'outer',
      content: '{name} {percentage}',
    },
    tooltip: {
      formatter: (datum: any) => {
        return {
          name: datum.type,
          value: `${datum.value}% (${datum.count}人)`,
        };
      },
    },
    interactions: [
      {
        type: 'element-active',
      },
    ],
  };

  // 关键词云配置
  const wordCloudConfig = {
    data: keywordData.map((item) => ({
      name: item.word,
      value: item.count,
    })),
    wordField: 'name',
    weightField: 'value',
    colorField: 'name',
    wordStyle: {
      fontFamily: 'Verdana',
      fontSize: [12, 60] as [number, number],
      rotation: 0,
    },
    random: () => 0.5,
  };

  return (
    <Modal
      title={
        <Space>
          <UserOutlined />
          {teacherDetail?.sso_teacher_name || '教师详情'}
        </Space>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={1000}
      style={{ top: 20 }}
    >
      <Spin spinning={loading}>
        {!teacherDetail ? (
          <Empty description="暂无教师详情数据" />
        ) : (
          <div>
            {/* 基本信息 */}
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="平均分"
                    value={teacherDetail.average_score}
                    precision={1}
                    suffix="分"
                    prefix={<TrophyOutlined style={{ color: '#faad14' }} />}
                    valueStyle={{ color: '#faad14' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="评价总数"
                    value={teacherDetail.total_evaluations}
                    suffix="次"
                    prefix={<MessageOutlined style={{ color: '#1890ff' }} />}
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="推荐率"
                    value={teacherDetail.recommendation_rate}
                    precision={1}
                    suffix="%"
                    prefix={<HeartOutlined style={{ color: '#52c41a' }} />}
                    valueStyle={{ color: '#52c41a' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <div style={{ textAlign: 'center' }}>
                    <div
                      style={{
                        fontSize: '14px',
                        color: '#999',
                        marginBottom: 8,
                      }}
                    >
                      教师信息
                    </div>
                    <div>
                      <Tag color="blue">
                        {teacherDetail.sso_teacher_subject || '未知学科'}
                      </Tag>
                    </div>
                    <div
                      style={{ fontSize: '12px', color: '#999', marginTop: 4 }}
                    >
                      {teacherDetail.sso_teacher_department || '未知部门'}
                    </div>
                  </div>
                </Card>
              </Col>
            </Row>

            {/* 图表区域 */}
            <Row gutter={16}>
              <Col span={12}>
                <Card title="评分分布" style={{ height: 400 }}>
                  {scoreDistribution.length === 0 ? (
                    <Empty description="暂无评分分布数据" />
                  ) : (
                    <Pie {...pieConfig} />
                  )}
                </Card>
              </Col>
              <Col span={12}>
                <Card title="家长评价关键词" style={{ height: 400 }}>
                  {keywordData.length === 0 ? (
                    <Empty description="暂无关键词数据" />
                  ) : (
                    <WordCloud {...wordCloudConfig} />
                  )}
                </Card>
              </Col>
            </Row>
          </div>
        )}
      </Spin>
    </Modal>
  );
};

export default TeacherDetailModal;
