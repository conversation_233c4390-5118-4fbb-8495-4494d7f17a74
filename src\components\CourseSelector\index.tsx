import { CheckOutlined, CloseOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import {
  Button,
  Card,
  Checkbox,
  Col,
  Empty,
  Row,
  Space,
  Spin,
  Tag,
  Typography,
} from 'antd';
import React, { useEffect, useState } from 'react';
import './index.less';

const { Title, Text } = Typography;

interface CourseSelectorProps {
  /** 学校编码 */
  schoolCode: string;
  /** 已选择的课程列表 */
  value?: API.ISelectedCourse[];
  /** 选择变化回调 */
  onChange?: (courses: API.ISelectedCourse[]) => void;
  /** 是否禁用 */
  disabled?: boolean;
}

/**
 * 课程选择器组件
 * @description 用于问卷创建时选择课程
 */
const CourseSelector: React.FC<CourseSelectorProps> = ({
  schoolCode,
  value = [],
  onChange,
  disabled = false,
}) => {
  const { schoolCourses, coursesLoading, fetchSchoolCourses } =
    useModel('questionnaire');
  const [selectedCourses, setSelectedCourses] =
    useState<API.ISelectedCourse[]>(value);

  // 初始化获取课程列表
  useEffect(() => {
    if (schoolCode) {
      fetchSchoolCourses(schoolCode);
    }
  }, [schoolCode, fetchSchoolCourses]);

  // 同步外部值变化
  useEffect(() => {
    setSelectedCourses(value);
  }, [value]);

  // 处理课程选择
  const handleCourseToggle = (course: API.ICourseInfo, checked: boolean) => {
    let newSelectedCourses: API.ISelectedCourse[];

    if (checked) {
      // 添加课程
      const selectedCourse: API.ISelectedCourse = {
        sso_course_id: course.id,
        sso_course_code: course.code,
        sso_course_name: course.name,
        section_code: course.sectionCode,
        section_name: course.sectionName,
      };
      newSelectedCourses = [...selectedCourses, selectedCourse];
    } else {
      // 移除课程
      newSelectedCourses = selectedCourses.filter(
        (selected) => selected.sso_course_id !== course.id,
      );
    }

    setSelectedCourses(newSelectedCourses);
    onChange?.(newSelectedCourses);
  };

  // 全选/取消全选
  const handleSelectAll = (checked: boolean) => {
    let newSelectedCourses: API.ISelectedCourse[];

    if (checked) {
      newSelectedCourses = schoolCourses.map((course) => ({
        sso_course_id: course.id,
        sso_course_code: course.code,
        sso_course_name: course.name,
        section_code: course.sectionCode,
        section_name: course.sectionName,
      }));
    } else {
      newSelectedCourses = [];
    }

    setSelectedCourses(newSelectedCourses);
    onChange?.(newSelectedCourses);
  };

  // 清空选择
  const handleClearAll = () => {
    setSelectedCourses([]);
    onChange?.([]);
  };

  // 检查课程是否已选择
  const isCourseSelected = (courseId: string) => {
    return selectedCourses.some(
      (selected) => selected.sso_course_id === courseId,
    );
  };

  // 按学段分组课程
  const groupedCourses = schoolCourses.reduce((groups, course) => {
    const sectionName = course.sectionName;
    if (!groups[sectionName]) {
      groups[sectionName] = [];
    }
    groups[sectionName].push(course);
    return groups;
  }, {} as Record<string, API.ICourseInfo[]>);

  const isAllSelected =
    schoolCourses.length > 0 && selectedCourses.length === schoolCourses.length;
  const isIndeterminate =
    selectedCourses.length > 0 && selectedCourses.length < schoolCourses.length;

  return (
    <div className="course-selector">
      <Card>
        <div className="course-selector-header">
          <Title level={5}>选择课程</Title>
          <Text type="secondary">
            选择要包含在问卷中的课程，只有教授这些课程的教师会出现在家长的评价列表中
          </Text>

          <div className="course-selector-actions">
            <Space>
              <Checkbox
                checked={isAllSelected}
                indeterminate={isIndeterminate}
                onChange={(e) => handleSelectAll(e.target.checked)}
                disabled={disabled || coursesLoading}
              >
                全选 ({selectedCourses.length}/{schoolCourses.length})
              </Checkbox>
              <Button
                size="small"
                onClick={handleClearAll}
                disabled={disabled || selectedCourses.length === 0}
                icon={<CloseOutlined />}
              >
                清空
              </Button>
            </Space>
          </div>
        </div>

        <Spin spinning={coursesLoading}>
          {schoolCourses.length === 0 ? (
            <Empty
              description="暂无课程数据"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          ) : (
            <div className="course-selector-content">
              {Object.entries(groupedCourses).map(([sectionName, courses]) => (
                <div key={sectionName} className="course-section">
                  <div className="section-header">
                    <Tag color="blue">{sectionName}</Tag>
                    <Text type="secondary">({courses.length} 门课程)</Text>
                  </div>

                  <Row gutter={[16, 16]} className="course-list">
                    {courses.map((course) => (
                      <Col key={course.id} xs={24} sm={12} md={8} lg={6}>
                        <Card
                          size="small"
                          className={`course-card ${
                            isCourseSelected(course.id) ? 'selected' : ''
                          }`}
                          hoverable={!disabled}
                          onClick={() => {
                            if (!disabled) {
                              handleCourseToggle(
                                course,
                                !isCourseSelected(course.id),
                              );
                            }
                          }}
                        >
                          <div className="course-card-content">
                            <div className="course-info">
                              <Text strong>{course.name}</Text>
                              <Text type="secondary" className="course-code">
                                {course.code}
                              </Text>
                            </div>
                            {isCourseSelected(course.id) && (
                              <CheckOutlined className="selected-icon" />
                            )}
                          </div>
                        </Card>
                      </Col>
                    ))}
                  </Row>
                </div>
              ))}
            </div>
          )}
        </Spin>

        {selectedCourses.length > 0 && (
          <div className="selected-summary">
            <Text type="secondary">
              已选择 {selectedCourses.length} 门课程：
              {selectedCourses
                .map((course) => course.sso_course_name)
                .join('、')}
            </Text>
          </div>
        )}
      </Card>
    </div>
  );
};

export default CourseSelector;
