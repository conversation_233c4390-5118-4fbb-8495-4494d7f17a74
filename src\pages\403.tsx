/*
 * @description: 鉴权失败界面
 * @author: zp<PERSON>, AI Assistant
 * @Date: 2021-07-14 17:11:16
 * @LastEditTime: 2025-06-20 09:03:44
 */
import { getQueryObj } from '@/utils/env';
import { useAccess } from '@umijs/max';
import { Button, Result } from 'antd';

const NotAuth = () => {
  // 检查是否是从第三方系统跳转过来的
  const { value } = getQueryObj();
  const { isLogin } = useAccess();

  if (!isLogin && value) {
    return null;
  }

  return (
    <Result
      status="403"
      title="认证失败"
      subTitle={
        value ? '认证信息处理失败，请联系管理员。' : '抱歉，您无权访问此页面。'
      }
      extra={
        <Button
          type="primary"
          onClick={() => {
            // 如果是在iframe中，尝试关闭当前窗口或跳转到父窗口
            if (window !== window.top) {
              try {
                window.parent.postMessage({ type: 'AUTH_FAILED' }, '*');
              } catch (e) {
                console.error('无法与父窗口通信', e);
              }
            }

            // 跳转到首页或重新加载
            window.location.href = '/';
          }}
        >
          返回首页
        </Button>
      }
    />
  );
};

export default NotAuth;
