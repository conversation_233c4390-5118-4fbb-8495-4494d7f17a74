/*
 * @description: 认证处理组件
 * @author: AI Assistant
 * @Date: 2023-07-10 10:00:00
 */
import {
  getSSOValue,
  getUserInfo,
  isAuthenticated,
  login,
  setSSOValue,
} from '@/utils/auth';
import { getQueryObj, removeQuery } from '@/utils/env';
import { history, useModel } from '@umijs/max';
import { Spin, message } from 'antd';
import React, { useEffect, useState } from 'react';

const AuthHandler: React.FC<{ children: JSX.Element }> = ({ children }) => {
  const { initialState, setInitialState } = useModel('@@initialState');
  const [loading, setLoading] = useState(false);
  const { value } = getQueryObj();

  useEffect(() => {
    const checkAuth = async () => {
      // 1. 如果已经通过全局状态认证，直接返回
      if (initialState) {
        // 检查本地存储是否一致，可选的同步步骤
        // 如果需要严格一致，可以在这里比较并更新
        const oldValue = getSSOValue();
        if (oldValue && oldValue !== value) {
          // 移除URL中的value参数，避免重复认证
          removeQuery('value');
          return;
        }
      }

      // 2. 检查URL中是否有value参数
      if (value) {
        // 3. 有value参数，进行快速认证
        setLoading(true);
        try {
          const authResult = await login(value);
          if (authResult.success) {
            // 认证成功，更新全局状态
            setSSOValue(value);
            const userInfo = getUserInfo(); // 重新获取最新用户信息
            if (userInfo) {
              await setInitialState({
                ...(initialState || {}),
                ...userInfo,
              });
            }
            // 移除URL中的value参数，避免重复认证
            removeQuery('value');
          } else {
            // 快速认证失败，跳转到未授权页面
            message.error('认证失败，请重新登录');
            history.push('/noAuth');
          }
        } catch (error) {
          // 处理快速认证过程中的异常
          console.error('认证处理错误:', error);
          message.error('认证处理发生错误');
          history.push('/noAuth');
        } finally {
          setLoading(false);
        }
      } else if (isAuthenticated()) {
        // 4. 没有value参数，但本地存储有认证信息（通过 isAuthenticated 判断）
        // 尝试使用本地信息更新全局状态
        const userInfo = getUserInfo();
        if (userInfo) {
          await setInitialState({
            ...(initialState || {}),
            ...userInfo,
          });
          // 如果认证信息有效，会继续渲染子组件
        } else {
          // 本地信息无效，清除并跳转未授权
          // isAuthenticated 内部应该已经处理了无效token的情况和跳转
          // 这里的 else 分支理论上不应该被频繁触发如果 isAuthenticated 逻辑正确
          history.push('/noAuth');
        }
      } else {
        // 5. 没有任何认证信息，跳转到未授权页面
        history.push('/noAuth');
      }
    };

    checkAuth();
  }, [initialState]); // 依赖 initialState，当用户状态变化时重新检查

  if (loading) {
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
        }}
      >
        <Spin size="large" tip="认证中..." />
      </div>
    );
  }

  return <>{children}</>;
};

export default AuthHandler;
