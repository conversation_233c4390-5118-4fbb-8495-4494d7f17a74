declare namespace API {
  /**
   * 统一返回信息格式
   */
  type ResType<T = any> = {
    errCode: number;
    data?: T;
    msg?: string;
  };

  /**
   * 星级模式枚举
   */
  enum StarMode {
    FIVE_STAR = 5,
    TEN_STAR = 10,
  }

  /**
   * 课程信息接口
   */
  interface ICourseInfo {
    /** 课程ID */
    id: string;
    /** 课程编码 */
    code: string;
    /** 课程名称 */
    name: string;
    /** 学段编码 */
    sectionCode: string;
    /** 学段名称 */
    sectionName: string;
    /** 是否内置课程 */
    buildIn: boolean;
    /** 学校编码 */
    enterpriseCode: string;
    /** 学校名称 */
    enterpriseName: string;
    /** 学期编码 */
    semesterCode: string;
    /** 学期名称 */
    semesterName: string;
  }

  /**
   * 选中的课程信息接口
   */
  interface ISelectedCourse {
    /** SSO课程ID */
    sso_course_id: string;
    /** SSO课程编码 */
    sso_course_code: string;
    /** SSO课程名称 */
    sso_course_name: string;
    /** 学段编码 */
    section_code: string;
    /** 学段名称 */
    section_name: string;
  }

  /**
   * 问卷关联课程信息接口
   */
  interface IQuestionnaireCourse extends ISelectedCourse {
    /** 是否启用 */
    is_enabled: boolean;
  }

  /**
   * 问卷创建参数接口
   */
  interface ICreateQuestionnaireParams {
    /** 问卷标题 */
    title: string;
    /** 问卷描述 */
    description?: string;
    /** 问卷月份，格式：YYYY-MM */
    month: string;
    /** SSO学校代码 */
    sso_school_code: string;
    /** 星级模式 */
    star_mode?: StarMode;
    /** 是否包含学校评价 */
    include_school_evaluation?: boolean;
    /** 问卷说明 */
    instructions?: string;
    /** 是否允许匿名 */
    allow_anonymous?: boolean;
    /** 最大评价教师数量 */
    max_teachers_limit?: number;
    /** 问卷开始时间 */
    start_time?: Date;
    /** 问卷结束时间 */
    end_time?: Date;
    /** 选择的课程列表 */
    selected_courses?: ISelectedCourse[];
  }

  /**
   * 问卷详情接口
   */
  interface IQuestionnaireDetail {
    /** 问卷ID */
    id: number;
    /** 问卷标题 */
    title: string;
    /** 问卷描述 */
    description?: string;
    /** SSO学校代码 */
    sso_school_code: string;
    /** 问卷月份 */
    month: string;
    /** 问卷状态 */
    status: string;
    /** 星级模式 */
    star_mode?: StarMode;
    /** 是否包含学校评价 */
    include_school_evaluation?: boolean;
    /** 问卷说明 */
    instructions?: string;
    /** 是否允许匿名 */
    allow_anonymous?: boolean;
    /** 最大评价教师数量 */
    max_teachers_limit?: number;
    /** 问卷开始时间 */
    start_time?: Date;
    /** 问卷结束时间 */
    end_time?: Date;
    /** 创建时间 */
    created_at: Date;
    /** 更新时间 */
    updated_at: Date;
    /** 选择的课程列表 */
    selected_courses?: ISelectedCourse[];
  }

  /**
   * 更新问卷状态参数接口
   */
  interface IUpdateQuestionnaireStatusParams {
    /** 问卷状态 */
    status: string;
  }

  /**
   * 提交问卷响应参数接口
   */
  interface ISubmitResponseParams {
    /** 问卷ID */
    questionnaire_id: number;
    /** 响应者信息 */
    respondent_info: {
      /** SSO学生代码 */
      sso_student_code: string;
      /** SSO学生姓名 */
      sso_student_name: string;
      /** SSO学生班级 */
      sso_student_class: string;
      /** SSO学生年级 */
      sso_student_grade: string;
      /** SSO学生班级编号 */
      class_code: string;
      /** SSO学生年级编号 */
      grade_code: string;
    };
    /** 教师评价列表 */
    teacher_evaluations: Array<{
      /** SSO教师ID */
      sso_teacher_id: string;
      /** SSO教师姓名 */
      sso_teacher_name: string;
      /** SSO教师科目 */
      sso_teacher_subject: string;
      /** 评分数据 */
      ratings: Record<string, number>;
      /** 文本评价 */
      comments?: string;
    }>;
  }

  /**
   * 响应查询参数接口
   */
  interface IResponseQuery {
    /** 问卷ID，可选 */
    questionnaire_id?: number;
    /** SSO学校代码，可选 */
    sso_school_code?: string;
    /** 月份，格式：YYYY-MM，可选 */
    month?: string;
    /** 页码，默认1，可选 */
    page?: number;
    /** 每页数量，默认10，可选 */
    limit?: number;
  }

  /**
   * 检查重复提交参数接口
   */
  interface ICheckDuplicateParams {
    /** 问卷ID */
    questionnaire_id: number;
    /** SSO学生代码 */
    sso_student_code: string;
  }

  interface IResponseDate {
    createdAt: Date;
    updatedAt: Date;
  }

  type LoginResponse = {
    token: string;
    refreshToken: string;
    user: Omit<User, 'password'>;
  };

  type User = {
    accessToken: string;
    refreshToken: string;
    userInfo: {
      id: string;
      userCode: string;
      username: string;
      realName: string;
      avatar?: string;
      enterpriseCode: string;
      enterpriseName: string;
      roles: string[];
      mobile: string;
      email: string;
      enterprise: {
        id: string;
        code: string;
        name: string;
        type_code: string;
        type_name: string;
        region: string;
        region_code: string;
      };
    };
    /** 跳转进来时携带的其他参数 */
    params?: Record<string, any>;
  };

  /**
   * 用户服务参数接口
   * @description User-Service parameters
   */
  interface IUserOptions {
    /** 用户ID */
    uid: number;
  }

  /**
   * 问卷查询参数接口
   * @description 用于问卷列表查询的参数定义
   */
  interface IQuestionnaireQuery {
    /** SSO学校代码，可选 */
    sso_school_code?: string;
    /** 月份，格式：YYYY-MM，可选 */
    month?: string;
    /** 问卷状态，可选 */
    status?: string;
    /** 页码，默认1，可选 */
    page?: number;
    /** 每页数量，默认10，可选 */
    limit?: number;
  }

  /**
   * 问卷列表响应接口
   * @description 问卷列表查询的响应数据结构
   */
  interface IQuestionnaireListResponse {
    /** 问卷列表数据 */
    list: any[];
    /** 总记录数 */
    total: number;
    /** 当前页码 */
    page: number;
    /** 每页数量 */
    limit: number;
  }

  /**
   * SSO学校信息接口
   * @description 从SSO系统获取的学校信息
   */
  interface ISSoSchoolInfo {
    /** 学校ID */
    id: string;
    /** 学校名称 */
    name: string;
    /** 学校状态（active/inactive） */
    status: string;
  }

  /**
   * SSO学生信息接口
   * @description 从SSO系统获取的学生信息
   */
  interface ISSoStudentInfo {
    /** 学生ID */
    id: string;
    /** 学生姓名 */
    name: string;
    /** 学生代码 */
    code: string;
    /** 学生班级 */
    class: string;
    /** 学生年级 */
    grade: string;
    /** 班级编号 */
    class_code: string;
    /** 年级编号 */
    grade_code: string;
    /** 学生状态（active/inactive） */
    status: string;
    /** 所属学校代码 */
    school_code: string;
  }

  /**
   * SSO教师信息接口
   * @description 从SSO系统获取的教师信息
   */
  interface ISSoTeacherInfo {
    /** 教师ID */
    id: string;
    /** 教师姓名 */
    name: string;
    /** 教师科目 */
    subject: string;
    /** 教师职位 */
    position: string;
    /** 教师部门 */
    department: string;
    /** 教师状态（active/inactive） */
    status: string;
    /** 所属学校代码 */
    school_code: string;
  }

  /**
   * 响应列表响应接口
   * @description 问卷响应列表查询的响应数据结构
   */
  interface IResponseListResponse {
    /** 响应列表数据 */
    list: any[];
    /** 总记录数 */
    total: number;
    /** 当前页码 */
    page: number;
    /** 每页数量 */
    limit: number;
  }

  /**
   * 响应统计信息接口
   * @description 问卷响应的统计数据
   */
  interface IResponseStatistics {
    /** 问卷ID */
    questionnaire_id: number;
    /** 总响应数 */
    total_responses: number;
    /** 已完成响应数 */
    completed_responses: number;
    /** 完成率（百分比） */
    completion_rate: number;
    /** 平均评分 */
    average_rating: number;
    /** 教师评价数量 */
    teacher_evaluation_count: number;
  }

  /**
   * 提交响应结果接口
   * @description 问卷响应提交成功后的返回数据
   */
  interface ISubmitResponseResult {
    /** 响应ID */
    response_id: number;
    /** 问卷ID */
    questionnaire_id: number;
    /** 提交时间 */
    submission_time: Date;
    /** 总平均分（百分制） */
    total_average_score: number;
    /** 评价教师数量 */
    teacher_count: number;
  }

  /**
   * 评分转换接口
   * @description 星级评分与百分制分数的转换关系
   */
  interface IRatingConversion {
    /** 星级（1-5或1-10） */
    star: number;
    /** 对应分数（百分制） */
    score: number;
    /** 评分描述 */
    description: string;
  }

  /**
   * 评分信息接口
   * @description 包含5星制和10星制的评分转换信息
   */
  interface IRatingInfo {
    /** 5星制评分信息 */
    five_star_mode: {
      /** 评分模式描述 */
      description: string;
      /** 转换规则说明 */
      conversion: string;
      /** 评分等级列表 */
      ratings: IRatingConversion[];
    };
    /** 10星制评分信息 */
    ten_star_mode: {
      /** 评分模式描述 */
      description: string;
      /** 转换规则说明 */
      conversion: string;
      /** 评分等级列表 */
      ratings: IRatingConversion[];
    };
  }

  /**
   * 趋势数据接口
   * @description 时间序列的趋势分析数据
   */
  interface ITrendData {
    /** 月份，格式：YYYY-MM */
    month: string;
    /** 总响应数，可选 */
    total_responses?: number;
    /** 已完成响应数，可选 */
    completed_responses?: number;
    /** 完成率（百分比），可选 */
    completion_rate?: number;
    /** 学校平均分，可选 */
    avg_school_score?: number;
    /** 教师平均分，可选 */
    avg_teacher_score?: number;
    /** 评价数量，可选 */
    evaluation_count?: number;
    /** 平均分，可选 */
    average_score?: number;
    /** 推荐率，可选 */
    recommendation_rate?: number;
  }

  /**
   * 操作日志列表响应接口
   * @description 操作日志列表查询的响应数据结构
   */
  interface IOperationLogListResponse {
    /** 操作日志列表数据 */
    list: any[];
    /** 总记录数 */
    total: number;
    /** 当前页码 */
    page: number;
    /** 每页数量 */
    limit: number;
  }

  /**
   * 操作日志统计接口
   * @description 操作日志的统计分析数据
   */
  interface IOperationLogStatistics {
    /** 总操作数 */
    total_operations: number;
    /** 成功操作数 */
    success_operations: number;
    /** 失败操作数 */
    failed_operations: number;
    /** 成功率（百分比） */
    success_rate: number;
    /** 平均响应时间（毫秒） */
    avg_response_time: number;
    /** 操作趋势数据 */
    operation_trend: any[];
    /** 模块分布数据 */
    module_distribution: any[];
    /** 用户活跃度数据 */
    user_activity: any[];
  }

  /**
   * 创建操作日志参数接口
   */
  interface ICreateOperationLogParams {
    /** 操作模块 */
    module: string;
    /** 操作类型 */
    operation_type: string;
    /** 操作描述 */
    description: string;
    /** 用户ID */
    user_id?: string;
    /** 学校代码 */
    school_code?: string;
    /** 操作结果 */
    result?: string;
    /** 响应时间（毫秒） */
    response_time?: number;
    /** 额外数据 */
    extra_data?: Record<string, any>;
  }

  /**
   * 操作日志查询参数接口
   */
  interface IOperationLogQuery {
    /** 操作模块，可选 */
    module?: string;
    /** 操作类型，可选 */
    operation_type?: string;
    /** 用户ID，可选 */
    user_id?: string;
    /** 学校代码，可选 */
    school_code?: string;
    /** 开始时间，可选 */
    start_time?: string;
    /** 结束时间，可选 */
    end_time?: string;
    /** 关键词搜索，可选 */
    keyword?: string;
    /** 日志级别，可选 */
    level?: 'info' | 'warn' | 'error';
    /** 页码，默认1，可选 */
    page?: number;
    /** 每页数量，默认10，可选 */
    limit?: number;
  }

  /**
   * 操作日志详情接口
   */
  interface IOperationLogDetail {
    /** 日志ID */
    id: number;
    /** 操作用户ID（SSO用户ID） */
    operator_user_id: string;
    /** 操作用户姓名 */
    operator_user_name?: string;
    /** 操作用户角色 */
    operator_user_role?: string;
    /** 操作用户所属学校编码 */
    operator_school_code?: string;
    /** 操作模块 */
    module: string;
    /** 操作类型 */
    operation_type: string;
    /** 操作描述 */
    operation_description: string;
    /** 操作目标ID */
    target_id?: string;
    /** 操作目标类型 */
    target_type?: string;
    /** 操作前数据（JSON格式） */
    before_data?: string;
    /** 操作后数据（JSON格式） */
    after_data?: string;
    /** 请求参数（JSON格式） */
    request_params?: string;
    /** 操作状态 */
    operation_status: string;
    /** 错误信息 */
    error_message?: string;
    /** 操作IP地址 */
    ip_address?: string;
    /** 用户代理信息 */
    user_agent?: string;
    /** 请求路径 */
    request_path?: string;
    /** 请求方法 */
    request_method?: string;
    /** 响应时间（毫秒） */
    response_time?: number;
    /** 操作时间 */
    operation_time: Date;
    /** 备注信息 */
    remarks?: string;
    /** 创建时间 */
    created_at: Date;
    /** 更新时间 */
    updated_at: Date;
  }

  // ==================== 家长端相关接口 ====================

  /**
   * 家长手机号验证参数接口
   */
  interface IParentPhoneVerifyParams {
    /** 家长手机号 */
    phone: string;
  }

  interface IEnterprise {
    id: string;
    code: string;
    name: string;
    english_name: string | null;
    type_code: string;
    type_name: string;
    region: string;
    region_code: string;
    liasion: string;
    mobile: string;
    address: string;
    social_credit_code: string;
    official_url: string | null;
    email: string;
    fax: string | null;
    postal_code: string;
    service_scope: string | null;
    legal_person: string | null;
    legal_person_code: string | null;
    section_name: string;
    section_alias_name: string;
    section_code: string;
    describe: string;
    school_motto: string;
    remark: string | null;
    honor: string;
    theme: string;
    style: string | null;
    logo_url: string;
    business_license_url: string;
    school_license_url: string;
    independent: boolean;
    hosted: boolean;
    hostedTime: string | null;
    showInitMessage: boolean;
    need_improve: boolean;
    teacher_need_improve: boolean;
    smart_screen_type: number;
    createdAt: string;
    updatedAt: string;
  }

  interface IClass {
    id: string;
    code: string;
    orderIndex: number;
    name: string;
    grade_code: string;
    grade_name: string;
    site_code: string;
    site_name: string;
    monitor: string | null;
    monitorCode: string | null;
    slogan: string;
    graduation: boolean;
    section_name: string;
    section_code: string;
    stage: string;
    graduation_semester: string;
    memberId: string;
    expectation: string;
    member_code: string | null;
    member_name: string | null;
    semesterCode: string;
    semesterName: string;
    enterpriseId: string;
    'class-student': {
      classId: string;
      studentId: string;
      code: string;
    };
    enterprise: IEnterprise;
  }

  interface IStudent {
    id: string;
    code: string;
    work_code: string;
    stage: string;
    graduation_semester: string;
    username: string | null;
    name: string;
    avatar: string | null;
    IDNumber: string | null;
    gender: string;
    mobile: string | null;
    email: string;
    graduation: boolean;
    graduationDate: string | null;
    status: string;
    remark: string | null;
    household_register: string | null;
    current_address: string | null;
    enterpriseId: string;
    facecode: string | null;
    facecodeMD5: string | null;
    faceImg: string | null;
    isNeedCard: boolean;
    cardInfo: string | null;
    outCardInfo: string | null;
    isUpdate: boolean;
    createdAt: string;
    updatedAt: string;
    class: IClass;
  }

  interface ParentChild {
    id: string;
    relation: string;
    is_guardian: boolean;
    parentId: string;
    studentId: string;
    student: IStudent;
  }

  interface ParentInfo {
    id: string;
    mobile: string;
    code: string;
    name: string;
    avatar: string;
    username: string;
    gender: string | null;
    email: string;
    address: string;
    facecode: string | null;
    facecodeMD5: string | null;
    faceImg: string | null;
    createdAt: string;
    updatedAt: string;
    children: ParentChild[];
  }

  /**
   * 家长手机号验证响应接口
   */
  interface IParentPhoneVerifyResponse {
    /** 验证是否成功 */
    is_valid: boolean;
    message?: string;
    /** 关联的学生列表 */
    parent?: ParentInfo;
  }

  /**
   * 获取学生教师列表参数接口
   */
  interface IGetTeacherListForClassParams {
    /** 学校代码 */
    enterpriseCode: string;
    /** 年级代码 */
    gradeCode: string;
    /** 班级代码 */
    classCode: string;
  }

  /**
   * 学生教师信息接口
   */
  interface IStudentTeacherInfo {
    /** 教师编号 */
    code: string;
    /** 课程编号 */
    courseCode: string;
    /** 课程名称 */
    courseName: string;
    /** 教师姓名 */
    name: string;
    /** 教师工号 */
    teacherCode: string;
    /** 用户类型 */
    type: 'teacher';
    /** 是否已评价 */
    is_evaluated: boolean;
  }

  /**
   * 问卷评价数据接口
   */
  interface IEvaluationData {
    /** 学校评分（0-100，百分制） */
    school_rating: number;
    /** 学校评价文本 */
    school_comment?: string;
    /** 教师评价列表 */
    teacher_evaluations: Array<{
      /** 教师ID */
      teacher_code: string;
      /** 教师评分（0-100，百分制） */
      rating: number;
      /** 教师评价文本 */
      comment?: string;
    }>;
  }

  /**
   * 家长端问卷提交参数接口
   */
  interface IParentSubmitEvaluationParams {
    /** 问卷ID */
    questionnaire_id: number;
    /** 家长手机号 */
    parent_phone: string;
    /** 家长姓名 */
    parent_name: string;
    /** SSO学生代码 */
    sso_student_code: string;
    sso_student_name: string;
    /** 问卷月份，格式：YYYY-MM */
    month: string;
    /** 学校评分 */
    school_rating: number;
    /** 学校评价描述 */
    school_description?: string;
    /** 教师评价列表 */
    teacher_evaluations: Array<{
      /** SSO教师ID */
      sso_teacher_id: string;
      /** 评分 */
      rating: number;
      /** 评价描述 */
      description?: string;
      /** 评价标签 */
      tags?: string[];
      /** 是否推荐 */
      is_recommended?: boolean;
    }>;
  }

  /**
   * 家长端问卷提交响应接口
   */
  interface IParentSubmitEvaluationResponse {
    /** 提交ID */
    submission_id: number;
    /** 提交时间 */
    submitted_at: Date;
    /** 评价的教师数量 */
    teacher_count: number;
    /** 学校平均分 */
    school_score: number;
  }

  /**
   * 获取问卷信息参数接口（家长端）
   */
  interface IGetQuestionnaireForParentParams {
    /** 学生ID */
    student_id: string;
    /** 月份，格式：YYYY-MM，可选 */
    month?: string;
  }

  /**
   * 获取学生问卷列表参数接口（家长端）
   */
  interface IGetStudentQuestionnairesParams {
    /** SSO学校代码 */
    sso_school_code: string;
    /** SSO学生代码 */
    sso_student_code: string;
    /** 家长手机号，可选 */
    parent_phone?: string;
  }

  /**
   * 问卷填写信息回填数据接口
   */
  interface IResponseEditData {
    /** 响应ID */
    response_id: number;
    /** 问卷ID */
    questionnaire_id: number;
    /** 问卷信息 */
    questionnaire: {
      id: number;
      title: string;
      month: string;
      star_rating_mode: number;
      status: string;
    };
    /** 家长手机号 */
    parent_phone: string;
    /** 家长姓名 */
    parent_name: string;
    /** SSO学生代码 */
    sso_student_code: string;
    /** SSO学生姓名 */
    sso_student_name: string;
    /** SSO学生班级 */
    sso_student_class: string;
    /** SSO学生年级 */
    sso_student_grade: string;
    /** 年级代码 */
    grade_code: string;
    /** 班级代码 */
    class_code: string;
    /** 月份 */
    month: string;
    /** 学校评分 */
    school_rating: number;
    /** 学校评价描述 */
    school_description?: string;
    /** 总平均分 */
    total_average_score: number;
    /** 教师数量 */
    teacher_count: number;
    /** 是否完成 */
    is_completed: boolean;
    /** 备注 */
    remarks?: string;
    /** 教师评价列表 */
    teacher_evaluations: Array<{
      /** SSO教师ID */
      sso_teacher_id: string;
      /** SSO教师姓名 */
      sso_teacher_name: string;
      /** SSO教师科目 */
      sso_teacher_subject: string;
      /** SSO教师职位 */
      sso_teacher_position?: string;
      /** SSO教师部门 */
      sso_teacher_department?: string;
      /** 评分 */
      rating: number;
      /** 评价描述 */
      description?: string;
    }>;
    /** 创建时间 */
    created_at: string;
    /** 更新时间 */
    updated_at: string;
  }

  /**
   * 家长端问卷信息接口
   */
  interface IParentQuestionnaireInfo {
    /** 问卷ID */
    questionnaire_id: number;
    /** 问卷标题 */
    title: string;
    /** 问卷描述 */
    description?: string;
    /** 问卷月份 */
    month: string;
    /** 学校信息 */
    school_info: {
      /** 学校ID */
      id: string;
      /** 学校名称 */
      name: string;
    };
    /** 班级信息 */
    class_info: {
      /** 班级名称 */
      name: string;
      /** 年级 */
      grade: string;
    };
    /** 星级模式（5或10） */
    star_mode: number;
    /** 是否包含学校评价 */
    include_school_evaluation?: boolean;
    /** 问卷状态 */
    status: string;
    /** 是否已提交 */
    is_submitted: boolean;
    /** 提交时间，如果已提交 */
    submitted_at?: Date;
    /** 问卷开始时间 */
    start_time?: Date;
    /** 问卷结束时间 */
    end_time?: Date;
  }

  /**
   * 学生问卷列表响应接口（家长端）
   */
  interface IStudentQuestionnairesResponse {
    /** 是否有问卷 */
    has_questionnaire: boolean;
    /** 消息 */
    message: string;
    /** 问卷信息，如果有问卷 */
    questionnaire?: {
      /** 问卷ID */
      id: number;
      /** 问卷标题 */
      title: string;
      /** 问卷描述 */
      description?: string;
      /** 问卷月份 */
      month: string;
      /** 星级模式 */
      star_mode: number;
      /** 是否包含学校评价 */
      include_school_evaluation?: boolean;
      /** SSO学校代码 */
      sso_school_code: string;
      /** SSO学校名称 */
      sso_school_name?: string;
      /** 问卷开始时间 */
      start_time?: Date;
      /** 问卷结束时间 */
      end_time?: Date;
      /** 问卷说明 */
      instructions?: string;
      /** 最大教师数量限制 */
      max_teachers_limit: number;
      /** 是否已提交 */
      is_submitted: boolean;
    };
  }

  /**
   * 学生问卷状态信息接口
   */
  interface IStudentQuestionnaireStatus {
    /** 学生ID */
    student_id: string;
    /** 学生姓名 */
    student_name: string;
    /** 学生班级 */
    student_class: string;
    /** 学生年级 */
    student_grade: string;
    /** 学校代码 */
    school_code: string;
    /** 学校名称 */
    school_name: string;
    /** 问卷列表 */
    questionnaires: Array<{
      /** 问卷ID */
      questionnaire_id: number;
      /** 问卷标题 */
      title: string;
      /** 问卷描述 */
      description?: string;
      /** 问卷月份 */
      month: string;
      /** 问卷状态 */
      status: string;
      /** 星级模式 */
      star_mode: number;
      /** 是否包含学校评价 */
      include_school_evaluation?: boolean;
      /** 是否已提交 */
      is_submitted: boolean;
      /** 提交时间 */
      submitted_at?: Date;
      /** 问卷开始时间 */
      start_time?: Date;
      /** 问卷结束时间 */
      end_time?: Date;
      /** 是否可填写（基于时间和状态） */
      is_available: boolean;
    }>;
  }
}
