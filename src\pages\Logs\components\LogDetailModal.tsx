import {
  CopyOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import {
  Button,
  Collapse,
  Descriptions,
  message,
  Modal,
  Space,
  Spin,
  Tag,
  Typography,
} from 'antd';
import dayjs from 'dayjs';
import React, { useState } from 'react';

const { Text, Paragraph } = Typography;

interface LogDetailModalProps {
  visible: boolean;
  loading?: boolean;
  data: API.IOperationLogDetail | null;
  onClose: () => void;
}

/**
 * 日志详情模态框组件
 */
const LogDetailModal: React.FC<LogDetailModalProps> = ({
  visible,
  loading = false,
  data,
  onClose,
}) => {
  const [copyLoading, setCopyLoading] = useState(false);

  // 获取操作状态标签
  const getStatusTag = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'success':
        return (
          <Tag color="success" icon={<InfoCircleOutlined />}>
            成功
          </Tag>
        );
      case 'failed':
      case 'error':
        return (
          <Tag color="error" icon={<ExclamationCircleOutlined />}>
            失败
          </Tag>
        );
      case 'warning':
        return (
          <Tag color="warning" icon={<WarningOutlined />}>
            警告
          </Tag>
        );
      default:
        return <Tag color="default">{status || '未知'}</Tag>;
    }
  };

  // 获取操作类型显示文本
  const getOperationTypeText = (type: string) => {
    const typeMap: Record<string, string> = {
      CREATE: '创建',
      UPDATE: '更新',
      DELETE: '删除',
      QUERY: '查询',
      SUBMIT: '提交',
      LOGIN: '登录',
      LOGOUT: '登出',
      EXPORT: '导出',
      IMPORT: '导入',
    };
    return typeMap[type] || type;
  };

  // 获取模块显示文本
  const getModuleText = (module: string) => {
    const moduleMap: Record<string, string> = {
      questionnaire: '问卷管理',
      response: '评价提交',
      statistics: '统计查询',
      user: '用户管理',
      auth: '用户认证',
      log: '日志管理',
    };
    return moduleMap[module] || module;
  };

  // 复制日志内容
  const handleCopy = async () => {
    if (!data) return;

    setCopyLoading(true);
    try {
      const logContent = JSON.stringify(data, null, 2);
      await navigator.clipboard.writeText(logContent);
      message.success('日志内容已复制到剪贴板');
    } catch (error) {
      message.error('复制失败，请手动选择复制');
    } finally {
      setCopyLoading(false);
    }
  };

  // 格式化JSON数据
  const formatJsonData = (data: any) => {
    if (!data) return '-';
    if (typeof data === 'string') return data;
    return JSON.stringify(data, null, 2);
  };

  return (
    <Modal
      title={
        <Space>
          {data && getStatusTag(data.operation_status)}
          操作日志详情
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={800}
      footer={[
        <Button
          key="copy"
          icon={<CopyOutlined />}
          loading={copyLoading}
          onClick={handleCopy}
        >
          复制内容
        </Button>,
        <Button key="close" onClick={onClose}>
          关闭
        </Button>,
      ]}
    >
      <Spin spinning={loading}>
        {data ? (
          <div>
            {/* 基本信息 */}
            <Descriptions title="基本信息" bordered column={2} size="small">
              <Descriptions.Item label="日志ID">{data.id}</Descriptions.Item>
              <Descriptions.Item label="操作时间">
                {dayjs(data.operation_time).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
              <Descriptions.Item label="操作状态">
                {getStatusTag(data.operation_status)}
              </Descriptions.Item>
              <Descriptions.Item label="操作模块">
                <Tag color="geekblue">{getModuleText(data.module)}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="操作类型">
                <Tag color="blue">
                  {getOperationTypeText(data.operation_type)}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="操作用户">
                {data.operator_user_name || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="用户角色">
                {data.operator_user_role || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="学校编码">
                {data.operator_school_code || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="响应时间">
                {data.response_time ? `${data.response_time}ms` : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="目标ID">
                {data.target_id || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="目标类型">
                {data.target_type || '-'}
              </Descriptions.Item>
            </Descriptions>

            {/* 请求信息 */}
            <Descriptions
              title="请求信息"
              bordered
              column={1}
              size="small"
              style={{ marginTop: 16 }}
            >
              <Descriptions.Item label="请求IP">
                <Text style={{ fontFamily: 'monospace' }}>
                  {data.ip_address || '-'}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="请求路径">
                <Text style={{ fontFamily: 'monospace' }}>
                  {data.request_path || '-'}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="请求方法">
                <Tag color="cyan">{data.request_method || '-'}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="用户代理">
                <Text ellipsis={{ tooltip: data.user_agent }}>
                  {data.user_agent || '-'}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="操作描述">
                <Paragraph copyable>{data.operation_description}</Paragraph>
              </Descriptions.Item>
              {data.error_message && (
                <Descriptions.Item label="错误信息">
                  <Text type="danger">{data.error_message}</Text>
                </Descriptions.Item>
              )}
              {data.remarks && (
                <Descriptions.Item label="备注信息">
                  <Text>{data.remarks}</Text>
                </Descriptions.Item>
              )}
            </Descriptions>

            {/* 详细数据 */}
            <div style={{ marginTop: 16 }}>
              <Collapse
                items={[
                  ...(data.request_params
                    ? [
                        {
                          key: 'request',
                          label: '请求参数',
                          children: (
                            <pre
                              style={{
                                background: '#f5f5f5',
                                padding: '12px',
                                borderRadius: '4px',
                                fontSize: '12px',
                                maxHeight: '200px',
                                overflow: 'auto',
                              }}
                            >
                              {formatJsonData(data.request_params)}
                            </pre>
                          ),
                        },
                      ]
                    : []),
                  ...(data.before_data
                    ? [
                        {
                          key: 'before',
                          label: '操作前数据',
                          children: (
                            <pre
                              style={{
                                background: '#f0f9ff',
                                padding: '12px',
                                borderRadius: '4px',
                                fontSize: '12px',
                                maxHeight: '200px',
                                overflow: 'auto',
                              }}
                            >
                              {formatJsonData(data.before_data)}
                            </pre>
                          ),
                        },
                      ]
                    : []),
                  ...(data.after_data
                    ? [
                        {
                          key: 'after',
                          label: '操作后数据',
                          children: (
                            <pre
                              style={{
                                background: '#f6ffed',
                                padding: '12px',
                                borderRadius: '4px',
                                fontSize: '12px',
                                maxHeight: '200px',
                                overflow: 'auto',
                              }}
                            >
                              {formatJsonData(data.after_data)}
                            </pre>
                          ),
                        },
                      ]
                    : []),
                  {
                    key: 'time',
                    label: '时间信息',
                    children: (
                      <Descriptions size="small" column={2}>
                        <Descriptions.Item label="操作时间">
                          {dayjs(data.operation_time).format(
                            'YYYY-MM-DD HH:mm:ss.SSS',
                          )}
                        </Descriptions.Item>
                        <Descriptions.Item label="创建时间">
                          {dayjs(data.created_at).format(
                            'YYYY-MM-DD HH:mm:ss.SSS',
                          )}
                        </Descriptions.Item>
                        <Descriptions.Item label="更新时间">
                          {dayjs(data.updated_at).format(
                            'YYYY-MM-DD HH:mm:ss.SSS',
                          )}
                        </Descriptions.Item>
                        <Descriptions.Item label="用户ID">
                          <Text style={{ fontFamily: 'monospace' }}>
                            {data.operator_user_id}
                          </Text>
                        </Descriptions.Item>
                      </Descriptions>
                    ),
                  },
                ]}
              />
            </div>
          </div>
        ) : (
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            暂无日志详情数据
          </div>
        )}
      </Spin>
    </Modal>
  );
};

export default LogDetailModal;
