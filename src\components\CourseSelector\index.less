.course-selector {
  .course-selector-header {
    margin-bottom: 16px;

    .ant-typography-title {
      margin-bottom: 8px;
    }

    .course-selector-actions {
      margin-top: 12px;
      padding-top: 12px;
      border-top: 1px solid #f0f0f0;
    }
  }

  .course-selector-content {
    .course-section {
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }

      .section-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 12px;
      }

      .course-list {
        .course-card {
          cursor: pointer;
          transition: all 0.3s ease;
          border: 2px solid transparent;

          &:hover {
            border-color: #1890ff;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 20%);
          }

          &.selected {
            border-color: #52c41a;
            background-color: #f6ffed;

            .ant-card-body {
              background-color: transparent;
            }
          }

          .course-card-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            min-height: 40px;

            .course-info {
              flex: 1;
              display: flex;
              flex-direction: column;
              gap: 4px;

              .course-code {
                font-size: 12px;
              }
            }

            .selected-icon {
              color: #52c41a;
              font-size: 16px;
              margin-left: 8px;
            }
          }
        }
      }
    }
  }

  .selected-summary {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;

    .ant-typography {
      word-break: break-all;
      line-height: 1.6;
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .course-selector {
    .course-selector-content {
      .course-section {
        .course-list {
          .course-card {
            .course-card-content {
              min-height: 36px;

              .course-info {
                gap: 2px;
              }
            }
          }
        }
      }
    }
  }
}
