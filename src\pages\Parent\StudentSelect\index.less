.student-select-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;

  .student-select-content {
    max-width: 800px;
    margin: 0 auto;

    .select-card {
      border-radius: 16px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 10%);
      border: none;

      .ant-card-body {
        padding: 32px;
      }
    }

    .card-header {
      margin-bottom: 32px;

      .back-button {
        margin-bottom: 16px;
        color: #1890ff;
        font-weight: 500;

        &:hover {
          color: #40a9ff;
          background: rgba(24, 144, 255, 10%);
        }
      }

      .header-info {
        text-align: center;
        margin-bottom: 16px;

        .header-title {
          margin-bottom: 8px !important;
          color: #262626;
        }

        .header-subtitle {
          font-size: 16px;
          color: #8c8c8c;
        }
      }

      .school-filter {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 16px;
        background: #f8f9fa;
        border-radius: 8px;
        margin-bottom: 16px;

        .ant-space {
          align-items: center;
        }

        .ant-select {
          .ant-select-selector {
            border-radius: 6px;
            border-color: #d9d9d9;

            &:hover {
              border-color: #40a9ff;
            }
          }

          &.ant-select-focused {
            .ant-select-selector {
              border-color: #1890ff;
              box-shadow: 0 0 0 2px rgba(24, 144, 255, 20%);
            }
          }
        }
      }
    }

    .student-list {
      margin-bottom: 24px;

      .student-card {
        border-radius: 12px;
        border: 2px solid #f0f0f0;
        transition: all 0.3s;
        cursor: default;

        &.available {
          &:hover {
            border-color: #1890ff;
            box-shadow: 0 8px 24px rgba(24, 144, 255, 15%);
            transform: translateY(-2px);
          }
        }

        &.disabled {
          opacity: 0.6;
          cursor: not-allowed;

          &:hover {
            border-color: #f0f0f0;
            box-shadow: none;
            transform: none;
          }
        }

        .ant-card-body {
          padding: 24px;
        }

        .student-info {
          display: flex;
          align-items: center;
          gap: 20px;

          .student-avatar {
            flex-shrink: 0;

            .ant-avatar {
              background: linear-gradient(135deg, #1890ff, #096dd9);
            }
          }

          .student-details {
            flex: 1;

            .student-name {
              display: flex;
              align-items: center;
              gap: 12px;
              margin-bottom: 8px;

              .ant-typography {
                margin: 0 !important;
                color: #262626;
              }
            }

            .student-class {
              margin-bottom: 8px;

              .ant-typography {
                font-size: 14px;
              }
            }

            .student-status {
              .ant-tag {
                margin-right: 8px;
                margin-bottom: 4px;
              }
            }

            .questionnaire-summary {
              margin-top: 8px;
              padding: 6px 8px;
              background: #f8f9fa;
              border-radius: 4px;
              border-left: 3px solid #1890ff;
            }
          }

          .student-action {
            flex-shrink: 0;

            .ant-btn {
              border-radius: 8px;
              font-weight: 500;
              background: linear-gradient(135deg, #1890ff, #096dd9);
              border: none;
              box-shadow: 0 4px 12px rgba(24, 144, 255, 30%);
              color: #fff;

              &:hover {
                background: linear-gradient(135deg, #40a9ff, #1890ff);
                transform: translateY(-1px);
                box-shadow: 0 6px 16px rgba(24, 144, 255, 40%);
              }
            }
          }
        }
      }
    }

    .select-tips {
      .ant-alert {
        border-radius: 8px;
        border: 1px solid #e6f7ff;

        .ant-alert-description {
          p {
            margin: 4px 0;
            color: #595959;
          }
        }
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .student-select-container {
    padding: 16px;

    .student-select-content {
      .select-card {
        .ant-card-body {
          padding: 20px;
        }
      }

      .card-header {
        margin-bottom: 24px;

        .header-info {
          .header-title {
            font-size: 20px;
          }

          .header-subtitle {
            font-size: 14px;
          }
        }
      }

      .student-list {
        .student-card {
          .student-info {
            flex-direction: column;
            text-align: center;
            gap: 16px;

            .student-details {
              .student-name {
                justify-content: center;
              }
            }

            .student-action {
              width: 100%;

              .ant-btn {
                width: 100%;
              }
            }
          }
        }
      }
    }
  }
}
