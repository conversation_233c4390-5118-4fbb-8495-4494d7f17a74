import {
  getCachedIncompleteStudents,
  getCachedStatistics,
  getIncompleteStudents,
  getSchoolStatistics,
  getStatisticsTaskStatus,
  getTeacherKeywords,
  getTeacherRanking,
  getTeacherScoreDistribution,
  getTeacherStatistics,
  triggerStatisticsTask,
} from '@/services';
import type {
  ICachedIncompleteStudentsQuery,
  ICachedIncompleteStudentsResponse,
  ICachedStatisticsResponse,
  IIncompleteStudentsQuery,
  IIncompleteStudentsResponse,
  IKeywordData,
  ISchoolStatistics,
  IScoreDistribution,
  IStatisticsQuery,
  IStatisticsTaskStatusResponse,
  ITeacherRanking,
  ITeacherStatistics,
  ITriggerStatisticsParams,
  StatisticsTaskStatus,
} from '@/types/statistics';
import { handleApiResponse } from '@/utils/errorHandler';
import { message } from 'antd';
import { useCallback, useState } from 'react';

/**
 * 统计分析数据模型
 */
export default function useStatisticsModel() {
  // 加载状态
  const [loading, setLoading] = useState(false);
  const [modalLoading, setModalLoading] = useState(false);

  // 学校统计数据
  const [schoolStatistics, setSchoolStatistics] =
    useState<ISchoolStatistics | null>(null);

  // 教师排名数据
  const [teacherRanking, setTeacherRanking] = useState<ITeacherRanking[]>([]);
  const [teacherTotal, setTeacherTotal] = useState(0);

  // 教师详情数据
  const [teacherDetail, setTeacherDetail] = useState<ITeacherStatistics | null>(
    null,
  );
  const [scoreDistribution, setScoreDistribution] = useState<
    IScoreDistribution[]
  >([]);
  const [keywordData, setKeywordData] = useState<IKeywordData[]>([]);

  // 筛选条件
  const [filters] = useState<IStatisticsQuery>({});

  // 未填写学生数据
  const [incompleteStudents, setIncompleteStudents] =
    useState<IIncompleteStudentsResponse | null>(null);
  const [incompleteStudentsLoading, setIncompleteStudentsLoading] =
    useState(false);

  // 统计任务相关状态
  const [statisticsTaskStatus, setStatisticsTaskStatus] =
    useState<IStatisticsTaskStatusResponse | null>(null);
  const [cachedStatistics, setCachedStatistics] =
    useState<ICachedStatisticsResponse | null>(null);
  const [cachedIncompleteStudents, setCachedIncompleteStudents] =
    useState<ICachedIncompleteStudentsResponse | null>(null);
  const [taskLoading, setTaskLoading] = useState(false);

  // 获取学校统计数据
  const fetchSchoolStatistics = useCallback(
    async (params?: IStatisticsQuery) => {
      setLoading(true);
      try {
        const response = await getSchoolStatistics(params);
        const result = handleApiResponse(response);

        if (result.success) {
          setSchoolStatistics(result.data || null);
          return result.data;
        } else {
          setSchoolStatistics(null);
          return null;
        }
      } catch (error) {
        message.error('获取学校统计数据失败');
        setSchoolStatistics(null);
        return null;
      } finally {
        setLoading(false);
      }
    },
    [],
  );

  // 获取教师排名数据
  const fetchTeacherRanking = useCallback(async (params?: IStatisticsQuery) => {
    setLoading(true);
    try {
      const response = await getTeacherRanking(params);
      const result = handleApiResponse(response);

      if (result.success) {
        setTeacherRanking(result.data?.list || []);
        setTeacherTotal(result.data?.total || 0);
        return result.data;
      } else {
        setTeacherRanking([]);
        setTeacherTotal(0);
        return null;
      }
    } catch (error) {
      message.error('获取教师排名失败');
      setTeacherRanking([]);
      setTeacherTotal(0);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // 获取教师详情数据
  const fetchTeacherDetail = useCallback(
    async (teacherId: string, params?: IStatisticsQuery) => {
      setModalLoading(true);
      try {
        // 并行获取教师统计、评分分布、关键词数据
        const [statsResponse, distributionResponse, keywordsResponse] =
          await Promise.all([
            getTeacherStatistics({ ...params, sso_teacher_id: teacherId }),
            getTeacherScoreDistribution(teacherId, params),
            getTeacherKeywords(teacherId, params),
          ]);

        // 处理教师统计数据
        const statsResult = handleApiResponse(statsResponse);
        if (statsResult.success) {
          setTeacherDetail(statsResult.data || null);
        }

        // 处理评分分布数据
        const distributionResult = handleApiResponse(distributionResponse);
        if (distributionResult.success) {
          setScoreDistribution(distributionResult.data || []);
        }

        // 处理关键词数据
        const keywordsResult = handleApiResponse(keywordsResponse);
        if (keywordsResult.success) {
          setKeywordData(keywordsResult.data || []);
        }

        return {
          detail: statsResult.data,
          distribution: distributionResult.data,
          keywords: keywordsResult.data,
        };
      } catch (error) {
        message.error('获取教师详情失败');
        setTeacherDetail(null);
        setScoreDistribution([]);
        setKeywordData([]);
        return null;
      } finally {
        setModalLoading(false);
      }
    },
    [],
  );

  // 获取未填写学生数据
  const fetchIncompleteStudents = useCallback(
    async (params: IIncompleteStudentsQuery) => {
      console.log('fetchIncompleteStudents called, params:', params);
      setIncompleteStudentsLoading(true);
      try {
        const response = await getIncompleteStudents(params);

        if (response.errCode === 0) {
          setIncompleteStudents(response.data || null);
          message.success('获取未填写学生数据成功');
          return response.data;
        } else {
          setIncompleteStudents(null);
          message.error(response.msg || '获取未填写学生数据失败');
          return null;
        }
      } catch (error) {
        setIncompleteStudents(null);
        message.error('网络错误，请稍后重试');
        return null;
      } finally {
        setIncompleteStudentsLoading(false);
      }
    },
    [],
  );

  // 清空教师详情数据
  const clearTeacherDetail = useCallback(() => {
    setTeacherDetail(null);
    setScoreDistribution([]);
    setKeywordData([]);
  }, []);

  // 清空未填写学生数据
  const clearIncompleteStudents = useCallback(() => {
    setIncompleteStudents(null);
  }, []);

  // 触发统计计算
  const triggerStatistics = useCallback(
    async (questionnaireId: number) => {
      setTaskLoading(true);
      try {
        const response = await triggerStatisticsTask({ questionnaire_id: questionnaireId });

        if (response.errCode === 0) {
          message.success('统计任务已启动');
          return response.data;
        } else {
          message.error(response.msg || '启动统计任务失败');
          return null;
        }
      } catch (error) {
        message.error('网络错误，请稍后重试');
        return null;
      } finally {
        setTaskLoading(false);
      }
    },
    [],
  );

  // 获取统计任务状态
  const fetchStatisticsTaskStatus = useCallback(
    async (questionnaireId: number) => {
      try {
        const response = await getStatisticsTaskStatus(questionnaireId);

        if (response.errCode === 0) {
          setStatisticsTaskStatus(response.data || null);
          return response.data;
        } else {
          setStatisticsTaskStatus(null);
          return null;
        }
      } catch (error) {
        message.error('获取统计状态失败');
        setStatisticsTaskStatus(null);
        return null;
      }
    },
    [],
  );

  // 获取缓存的统计数据
  const fetchCachedStatistics = useCallback(
    async (questionnaireId: number) => {
      setLoading(true);
      try {
        const response = await getCachedStatistics(questionnaireId);

        if (response.errCode === 0) {
          setCachedStatistics(response.data || null);

          // 将缓存数据转换为现有格式
          if (response.data) {
            const schoolStats: ISchoolStatistics = {
              sso_school_code: '', // 需要从其他地方获取
              sso_school_name: '', // 需要从其他地方获取
              month: '', // 需要从其他地方获取
              total_responses: response.data.submitted_count,
              completed_responses: response.data.submitted_count,
              total_students: response.data.total_students,
              completion_rate: response.data.completion_rate,
              school_average_score: response.data.school_average_score,
              teacher_average_score: response.data.teacher_average_score,
              total_teachers_evaluated: response.data.total_teachers,
            };
            setSchoolStatistics(schoolStats);

            // 转换教师排名数据
            const teacherRankingData: ITeacherRanking[] = response.data.teacher_ranking.map((item, index) => ({
              sso_teacher_id: item.teacher_id,
              sso_teacher_name: item.teacher_name,
              sso_teacher_subject: '', // 需要从其他地方获取
              sso_teacher_department: '', // 需要从其他地方获取
              average_score: item.average_score,
              evaluation_count: item.evaluation_count,
              recommendation_rate: 0, // 需要从其他地方获取
              rank: index + 1,
            }));
            setTeacherRanking(teacherRankingData);
            setTeacherTotal(response.data.teacher_ranking.length);
          }

          return response.data;
        } else {
          setCachedStatistics(null);
          return null;
        }
      } catch (error) {
        message.error('获取缓存统计数据失败');
        setCachedStatistics(null);
        return null;
      } finally {
        setLoading(false);
      }
    },
    [],
  );

  // 获取缓存的未填写学生数据
  const fetchCachedIncompleteStudents = useCallback(
    async (params: ICachedIncompleteStudentsQuery) => {
      setIncompleteStudentsLoading(true);
      try {
        const response = await getCachedIncompleteStudents(params);

        if (response.errCode === 0) {
          setCachedIncompleteStudents(response.data || null);

          // 将缓存数据转换为现有格式
          if (response.data) {
            const convertedData: IIncompleteStudentsResponse = {
              summary: {
                total_incomplete: response.data.total,
                by_grade: [], // 需要从缓存数据中提取
                by_class: [], // 需要从缓存数据中提取
              },
              pagination: {
                page: response.data.page,
                pageSize: response.data.pageSize,
                total: response.data.total,
                totalPages: response.data.totalPages,
              },
              classes: [], // 需要从缓存数据中转换
            };
            setIncompleteStudents(convertedData);
          }

          return response.data;
        } else {
          setCachedIncompleteStudents(null);
          message.error(response.msg || '获取缓存未填写学生数据失败');
          return null;
        }
      } catch (error) {
        setCachedIncompleteStudents(null);
        message.error('网络错误，请稍后重试');
        return null;
      } finally {
        setIncompleteStudentsLoading(false);
      }
    },
    [],
  );

  return {
    // 状态
    loading,
    modalLoading,
    schoolStatistics,
    teacherRanking,
    teacherTotal,
    teacherDetail,
    scoreDistribution,
    keywordData,
    filters,
    incompleteStudents,
    incompleteStudentsLoading,

    // 统计任务相关状态
    statisticsTaskStatus,
    cachedStatistics,
    cachedIncompleteStudents,
    taskLoading,

    // 方法
    fetchSchoolStatistics,
    fetchTeacherRanking,
    fetchTeacherDetail,
    fetchIncompleteStudents,
    clearTeacherDetail,
    clearIncompleteStudents,

    // 统计任务相关方法
    triggerStatistics,
    fetchStatisticsTaskStatus,
    fetchCachedStatistics,
    fetchCachedIncompleteStudents,
  };
}
