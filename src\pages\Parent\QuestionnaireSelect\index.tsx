import {
  ArrowLeftOutlined,
  ArrowRightOutlined,
  CalendarOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  TeamOutlined,
} from '@ant-design/icons';
import { useModel } from '@umijs/max';
import {
  <PERSON><PERSON>,
  Button,
  Card,
  Divider,
  Empty,
  Space,
  Tag,
  Typography,
} from 'antd';
import dayjs from 'dayjs';
import React from 'react';
import './index.less';

const { Title, Text } = Typography;

/**
 * 问卷选择页面
 */
const QuestionnaireSelect: React.FC = () => {
  const {
    loading,
    selectedStudent,
    questionnaireList,
    selectQuestionnaire,
    goBack,
  } = useModel('parent');

  // 处理问卷选择
  const handleSelectQuestionnaire = async (
    questionnaire: API.IParentQuestionnaireInfo,
  ) => {
    await selectQuestionnaire(questionnaire);
  };

  // 获取问卷状态标签
  const getStatusTag = (questionnaire: API.IParentQuestionnaireInfo) => {
    if (questionnaire.is_submitted) {
      return (
        <Tag color="success" icon={<CheckCircleOutlined />}>
          已提交
        </Tag>
      );
    }

    // 检查是否在有效期内
    const now = dayjs();
    const startTime = questionnaire.start_time
      ? dayjs(questionnaire.start_time)
      : null;
    const endTime = questionnaire.end_time
      ? dayjs(questionnaire.end_time)
      : null;

    if (startTime && now.isBefore(startTime)) {
      return (
        <Tag color="default" icon={<ClockCircleOutlined />}>
          未开始
        </Tag>
      );
    }

    if (endTime && now.isAfter(endTime)) {
      return (
        <Tag color="error" icon={<ClockCircleOutlined />}>
          已截止
        </Tag>
      );
    }

    return (
      <Tag color="processing" icon={<ClockCircleOutlined />}>
        待填写
      </Tag>
    );
  };

  // 检查问卷是否可填写
  const isQuestionnaireAvailable = (
    questionnaire: API.IParentQuestionnaireInfo,
  ) => {
    if (questionnaire.is_submitted) {
      return false;
    }

    const now = dayjs();
    const startTime = questionnaire.start_time
      ? dayjs(questionnaire.start_time)
      : null;
    const endTime = questionnaire.end_time
      ? dayjs(questionnaire.end_time)
      : null;

    if (startTime && now.isBefore(startTime)) {
      return false;
    }

    if (endTime && now.isAfter(endTime)) {
      return false;
    }

    return true;
  };

  // 检查问卷是否可以点击（包括查看已提交的问卷）
  const isQuestionnaireClickable = (
    questionnaire: API.IParentQuestionnaireInfo,
  ) => {
    // 已提交的问卷可以点击查看
    if (questionnaire.is_submitted) {
      return true;
    }

    // 其他情况使用原有的可填写逻辑
    return isQuestionnaireAvailable(questionnaire);
  };

  // 按班级分组问卷
  const groupedQuestionnaires = questionnaireList.reduce(
    (groups, questionnaire) => {
      const className = questionnaire.class_info.name;
      if (!groups[className]) {
        groups[className] = [];
      }
      groups[className].push(questionnaire);
      return groups;
    },
    {} as Record<string, API.IParentQuestionnaireInfo[]>,
  );

  if (!selectedStudent) {
    return null;
  }

  return (
    <div className="questionnaire-select-container">
      <div className="questionnaire-select-content">
        <Card className="select-card">
          {/* 头部信息 */}
          <div className="card-header">
            <Button
              type="text"
              icon={<ArrowLeftOutlined />}
              onClick={goBack}
              className="back-button"
            >
              返回
            </Button>

            <div className="header-info">
              <Title level={3} className="header-title">
                选择问卷
              </Title>
              <Text type="secondary" className="header-subtitle">
                学生：{selectedStudent.name} | 年级：{selectedStudent.grade}
              </Text>
            </div>
          </div>

          {/* 问卷列表 */}
          <div className="questionnaire-list">
            {questionnaireList.length === 0 ? (
              <Empty
                description="暂无可填写的问卷"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            ) : (
              <Space
                direction="vertical"
                size="large"
                style={{ width: '100%' }}
              >
                {Object.entries(groupedQuestionnaires).map(
                  ([className, questionnaires]) => (
                    <div key={className} className="class-group">
                      <div className="class-header">
                        <TeamOutlined />
                        <span className="class-name">{className}</span>
                        <Tag color="blue">
                          {questionnaires[0].class_info.grade}
                        </Tag>
                      </div>

                      <Space
                        direction="vertical"
                        size="middle"
                        style={{ width: '100%' }}
                      >
                        {questionnaires.map((questionnaire) => (
                          <Card
                            key={questionnaire.questionnaire_id}
                            className={`questionnaire-card ${
                              isQuestionnaireClickable(questionnaire)
                                ? questionnaire.is_submitted
                                  ? 'submitted'
                                  : 'available'
                                : 'disabled'
                            }`}
                            hoverable={isQuestionnaireClickable(questionnaire)}
                            onClick={() =>
                              isQuestionnaireClickable(questionnaire) &&
                              handleSelectQuestionnaire(questionnaire)
                            }
                          >
                            <div className="questionnaire-info">
                              <div className="questionnaire-main">
                                <div className="questionnaire-title">
                                  <Title level={4}>{questionnaire.title}</Title>
                                  {getStatusTag(questionnaire)}
                                </div>

                                <div className="questionnaire-details">
                                  <Space split={<Divider type="vertical" />}>
                                    <Text type="secondary">
                                      <CalendarOutlined />{' '}
                                      {dayjs(questionnaire.month).format(
                                        'YYYY年MM月',
                                      )}
                                    </Text>
                                    <Text type="secondary">
                                      {questionnaire.star_mode}星制评分
                                    </Text>
                                    <Text type="secondary">
                                      学校：{questionnaire.school_info.name}
                                    </Text>
                                  </Space>
                                </div>

                                {questionnaire.description && (
                                  <div className="questionnaire-description">
                                    <Text type="secondary">
                                      {questionnaire.description}
                                    </Text>
                                  </div>
                                )}

                                {/* 时间信息 */}
                                {(questionnaire.start_time ||
                                  questionnaire.end_time) && (
                                  <div className="questionnaire-time">
                                    <Text
                                      type="secondary"
                                      className="time-info"
                                    >
                                      {questionnaire.start_time && (
                                        <span>
                                          开始时间：
                                          {dayjs(
                                            questionnaire.start_time,
                                          ).format('YYYY-MM-DD HH:mm')}
                                        </span>
                                      )}
                                      {questionnaire.start_time &&
                                        questionnaire.end_time &&
                                        ' | '}
                                      {questionnaire.end_time && (
                                        <span>
                                          截止时间：
                                          {dayjs(questionnaire.end_time).format(
                                            'YYYY-MM-DD HH:mm',
                                          )}
                                        </span>
                                      )}
                                    </Text>
                                  </div>
                                )}
                              </div>

                              <div className="questionnaire-action">
                                {isQuestionnaireAvailable(questionnaire) ? (
                                  <Button
                                    type="primary"
                                    icon={<ArrowRightOutlined />}
                                    loading={loading}
                                    size="large"
                                  >
                                    开始评价
                                  </Button>
                                ) : questionnaire.is_submitted ? (
                                  <Button
                                    type="default"
                                    icon={<ArrowRightOutlined />}
                                    loading={loading}
                                    size="large"
                                  >
                                    查看详情
                                  </Button>
                                ) : (
                                  <Button disabled size="large">
                                    不可填写
                                  </Button>
                                )}
                              </div>
                            </div>
                          </Card>
                        ))}
                      </Space>
                    </div>
                  ),
                )}
              </Space>
            )}
          </div>

          {/* 提示信息 */}
          <div className="select-tips">
            <Alert
              message="填写说明"
              description={
                <div>
                  <p>• 每个问卷只能填写一次，请认真评价</p>
                  <p>• 问卷按班级分组显示，请选择对应的问卷</p>
                  <p>• 已提交的问卷可以点击查看详情</p>
                  <p>• 请在规定时间内完成问卷填写</p>
                </div>
              }
              type="info"
              showIcon
            />
          </div>
        </Card>
      </div>
    </div>
  );
};

export default QuestionnaireSelect;
