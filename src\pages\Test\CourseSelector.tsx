import CourseSelector from '@/components/CourseSelector';
import { Card } from 'antd';
import React, { useState } from 'react';

/**
 * 课程选择器测试页面
 */
const CourseSelectorTest: React.FC = () => {
  const [selectedCourses, setSelectedCourses] = useState<API.ISelectedCourse[]>(
    [],
  );

  return (
    <div style={{ padding: 24 }}>
      <Card title="课程选择器测试">
        <CourseSelector
          schoolCode="school_001" // 测试用的学校编码
          value={selectedCourses}
          onChange={setSelectedCourses}
        />

        <div style={{ marginTop: 24 }}>
          <h3>选中的课程：</h3>
          <pre>{JSON.stringify(selectedCourses, null, 2)}</pre>
        </div>
      </Card>
    </div>
  );
};

export default CourseSelectorTest;
