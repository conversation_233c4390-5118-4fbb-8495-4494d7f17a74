/*
 * @description: 认证相关工具函数
 * @author: AI Assistant
 * @Date: 2023-07-10 10:00:00
 */
import {
  login as loginService,
  logout as logoutService,
  refreshToken as refreshTokenService,
} from '@/services/sso';

/** 存储SSO认证串，用于判断新登录是否需要强制触发 */
export const setSSOValue = (value: any) => {
  localStorage.setItem(localStorage_prefix + 'ssoValue', value);
};

/** 获取SSO认证串 */
export const getSSOValue = () => {
  const valueStr = localStorage.getItem(localStorage_prefix + 'ssoValue');
  return valueStr;
};

/**
 * 获取访问令牌
 * @returns 访问令牌
 */
export const getAccessToken = (): string => {
  return localStorage.getItem(localStorage_prefix + 'accessToken') || '';
};

/**
 * 获取刷新令牌
 * @returns 刷新令牌
 */
export const getRefreshToken = (): string => {
  return localStorage.getItem(localStorage_prefix + 'refreshToken') || '';
};

/**
 * 获取用户信息
 * @returns 用户信息
 */
export const getUserInfo = (): any => {
  const userInfoStr = localStorage.getItem(localStorage_prefix + 'userInfo');
  if (userInfoStr) {
    try {
      return JSON.parse(userInfoStr);
    } catch (error) {
      return null;
    }
  }
  return null;
};

/**
 * 清除认证信息
 */
export const clearAuthData = () => {
  localStorage.removeItem(localStorage_prefix + 'accessToken');
  localStorage.removeItem(localStorage_prefix + 'refreshToken');
  localStorage.removeItem(localStorage_prefix + 'userInfo');
};

/**
 * 登录函数（用于wrapper和403页面的快速认证）
 * @param code 授权码
 * @returns 登录结果
 */
export const login = async (code: string) => {
  try {
    const { errCode, msg, data } = await loginService({ code });

    if (errCode === 0) {
      // 保存令牌
      localStorage.setItem(
        localStorage_prefix + 'accessToken',
        data!.accessToken,
      );
      localStorage.setItem(
        localStorage_prefix + 'refreshToken',
        data!.refreshToken,
      );
      // 保存用户信息
      localStorage.setItem(
        localStorage_prefix + 'userInfo',
        JSON.stringify(data!.userInfo),
      );

      return {
        success: true,
        data,
        message: '登录成功',
      };
    } else {
      return {
        success: false,
        error: msg || '登录失败',
      };
    }
  } catch (error) {
    console.error('登录错误:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : '登录失败',
    };
  }
};

/**
 * 刷新令牌函数
 * @returns 刷新结果
 */
export const refreshToken = async () => {
  try {
    const refreshTokenValue = localStorage.getItem(
      localStorage_prefix + 'refreshToken',
    );
    if (!refreshTokenValue) {
      throw new Error('刷新令牌不存在');
    }

    const response = await refreshTokenService({
      refreshToken: refreshTokenValue,
    });

    if (response.errCode === 0) {
      // 更新令牌
      localStorage.setItem(
        localStorage_prefix + 'accessToken',
        response.data.accessToken,
      );
      localStorage.setItem(
        localStorage_prefix + 'refreshToken',
        response.data.refreshToken,
      );
      return true;
    } else {
      // 刷新失败，清除认证信息
      clearAuthData();
      return false;
    }
  } catch (error) {
    // 刷新失败，清除认证信息
    clearAuthData();
    return false;
  }
};

/**
 * 退出登录
 */
export const logout = async () => {
  try {
    const accessToken = localStorage.getItem(
      localStorage_prefix + 'accessToken',
    );
    if (accessToken) {
      await logoutService();
    }
  } finally {
    // 清除本地存储的令牌和用户信息
    clearAuthData();
  }
};

/**
 * 检查是否已认证
 * @returns 是否已认证
 */
export const isAuthenticated = (): boolean => {
  return !!getAccessToken();
};
