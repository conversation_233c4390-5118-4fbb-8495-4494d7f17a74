.questionnaire-select-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;

  .questionnaire-select-content {
    width: 100%;
    max-width: 800px;

    .select-card {
      border-radius: 16px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 10%);
      border: none;
      overflow: hidden;

      .card-header {
        display: flex;
        align-items: center;
        margin-bottom: 24px;
        padding-bottom: 16px;
        border-bottom: 1px solid #f0f0f0;

        .back-button {
          margin-right: 16px;
          color: #666;

          &:hover {
            color: #1890ff;
            background-color: #f0f8ff;
          }
        }

        .header-info {
          flex: 1;

          .header-title {
            margin: 0;
            color: #262626;
            font-weight: 600;
          }

          .header-subtitle {
            font-size: 14px;
            margin-top: 4px;
          }
        }
      }

      .questionnaire-list {
        margin-bottom: 24px;

        .class-group {
          .class-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 16px;
            padding: 12px 16px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #1890ff;

            .class-name {
              font-weight: 600;
              font-size: 16px;
              color: #262626;
            }
          }

          .questionnaire-card {
            border-radius: 12px;
            border: 2px solid #f0f0f0;
            transition: all 0.3s ease;
            cursor: pointer;

            &.available {
              &:hover {
                border-color: #1890ff;
                box-shadow: 0 8px 24px rgba(24, 144, 255, 12%);
                transform: translateY(-2px);
              }
            }

            &.submitted {
              background-color: #f6ffed;
              border-color: #b7eb8f;

              &:hover {
                border-color: #52c41a;
                box-shadow: 0 8px 24px rgba(82, 196, 26, 12%);
                transform: translateY(-2px);
              }
            }

            &.disabled {
              background-color: #fafafa;
              cursor: not-allowed;
              opacity: 0.7;

              .questionnaire-info {
                opacity: 0.6;
              }
            }

            .questionnaire-info {
              display: flex;
              align-items: center;
              justify-content: space-between;

              .questionnaire-main {
                flex: 1;
                margin-right: 16px;

                .questionnaire-title {
                  display: flex;
                  align-items: center;
                  gap: 12px;
                  margin-bottom: 12px;

                  h4 {
                    margin: 0;
                    color: #262626;
                    font-weight: 600;
                  }
                }

                .questionnaire-details {
                  margin-bottom: 8px;

                  .ant-typography {
                    font-size: 13px;
                  }
                }

                .questionnaire-description {
                  margin-bottom: 8px;

                  .ant-typography {
                    font-size: 13px;
                    line-height: 1.5;
                  }
                }

                .questionnaire-time {
                  .time-info {
                    font-size: 12px;
                    color: #8c8c8c;
                  }
                }
              }

              .questionnaire-action {
                flex-shrink: 0;
              }
            }
          }
        }
      }

      .select-tips {
        .ant-alert {
          border-radius: 8px;
          border: none;
          background: #f6ffed;

          .ant-alert-description {
            p {
              margin: 4px 0;
              font-size: 13px;
            }
          }
        }
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .questionnaire-select-container {
    padding: 10px;

    .questionnaire-select-content {
      .select-card {
        border-radius: 12px;
        margin: 0;

        .card-header {
          .header-info {
            .header-title {
              font-size: 20px;
            }

            .header-subtitle {
              font-size: 13px;
            }
          }
        }

        .questionnaire-list {
          .class-group {
            .class-header {
              padding: 10px 12px;

              .class-name {
                font-size: 15px;
              }
            }

            .questionnaire-card {
              .questionnaire-info {
                flex-direction: column;
                align-items: stretch;

                .questionnaire-main {
                  margin-right: 0;
                  margin-bottom: 16px;

                  .questionnaire-title {
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 8px;

                    h4 {
                      font-size: 16px;
                    }
                  }
                }

                .questionnaire-action {
                  width: 100%;

                  .ant-btn {
                    width: 100%;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

// 小屏幕适配
@media (max-width: 480px) {
  .questionnaire-select-container {
    .questionnaire-select-content {
      .select-card {
        .questionnaire-list {
          .class-group {
            .questionnaire-card {
              .questionnaire-info {
                .questionnaire-main {
                  .questionnaire-details {
                    .ant-space {
                      flex-direction: column;
                      align-items: flex-start;
                      gap: 4px;

                      .ant-space-item {
                        margin: 0;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
