import { message } from 'antd';

/**
 * 简化的错误处理工具类
 * 统一处理API响应和异常，自动显示消息
 */

/**
 * 统一处理API响应
 * @param response API响应
 * @param successMessage 成功消息（可选）
 * @param errorMessage 错误消息（可选，不传则使用后台返回的错误信息）
 */
export function handleApiResponse<T = any>(
  response: API.ResType<T>,
  successMessage?: string,
  errorMessage?: string,
): { success: boolean; data?: T; error?: string } {
  if (response.errCode === 0) {
    if (successMessage) {
      message.success(successMessage);
    }
    return { success: true, data: response.data };
  } else {
    const finalErrorMessage = response.msg || errorMessage || '操作失败';
    message.error(finalErrorMessage);
    return { success: false, error: finalErrorMessage };
  }
}

// 简化的错误处理工具，只保留核心功能
